package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备数据备份操作记录实体类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("device_data_backup_record")
public class DeviceDataBackupRecord {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 备份批次ID */
    @TableField("backup_batch_id")
    private String backupBatchId;

    /** 原始检测数据ID */
    @TableField("original_detection_data_id")
    private Long originalDetectionDataId;

    /** 设备编码 */
    @TableField("device_code")
    private String deviceCode;

    /** 文件名 */
    @TableField("file_name")
    private String fileName;

    /** 备份原因 */
    @TableField("backup_reason")
    private String backupReason;

    /** 备份状态：SUCCESS-成功，FAILED-失败 */
    @TableField("backup_status")
    private String backupStatus;

    /** 备份消息 */
    @TableField("backup_message")
    private String backupMessage;

    /** 原始文件路径 */
    @TableField("original_file_path")
    private String originalFilePath;

    /** 备份文件路径 */
    @TableField("backup_file_path")
    private String backupFilePath;

    /** 备份时间 */
    @TableField("backup_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime backupTime;

    /** 创建人 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 备份状态枚举
     */
    public enum BackupStatus {
        SUCCESS("SUCCESS", "成功"),
        FAILED("FAILED", "失败");

        private final String code;
        private final String description;

        BackupStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
