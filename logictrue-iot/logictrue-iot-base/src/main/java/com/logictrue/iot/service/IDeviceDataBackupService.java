package com.logictrue.iot.service;

import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.entity.DeviceDataBackupRecord;

import java.util.List;

/**
 * 设备数据备份服务接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IDeviceDataBackupService {

    /**
     * 备份检测数据及其关联数据
     * 
     * @param detectionData 要备份的检测数据
     * @param backupBatchId 备份批次ID
     * @param backupReason 备份原因
     * @return 备份记录
     */
    DeviceDataBackupRecord backupDetectionData(DeviceDetectionData detectionData, 
                                              String backupBatchId, 
                                              String backupReason);

    /**
     * 批量备份检测数据
     * 
     * @param detectionDataList 要备份的检测数据列表
     * @param backupBatchId 备份批次ID
     * @param backupReason 备份原因
     * @return 备份记录列表
     */
    List<DeviceDataBackupRecord> batchBackupDetectionData(List<DeviceDetectionData> detectionDataList,
                                                         String backupBatchId,
                                                         String backupReason);

    /**
     * 备份原始文件
     * 
     * @param originalFilePath 原始文件路径
     * @param deviceCode 设备编码
     * @param fileName 文件名
     * @return 备份文件路径
     */
    String backupOriginalFile(String originalFilePath, String deviceCode, String fileName);

    /**
     * 恢复备份数据
     * 
     * @param backupBatchId 备份批次ID
     * @return 是否恢复成功
     */
    boolean restoreBackupData(String backupBatchId);

    /**
     * 删除备份数据
     * 
     * @param backupBatchId 备份批次ID
     * @param deleteFiles 是否删除备份文件
     * @return 是否删除成功
     */
    boolean deleteBackupData(String backupBatchId, boolean deleteFiles);

    /**
     * 获取备份记录
     * 
     * @param backupBatchId 备份批次ID
     * @return 备份记录列表
     */
    List<DeviceDataBackupRecord> getBackupRecords(String backupBatchId);

    /**
     * 清理过期备份数据
     * 
     * @param expireDays 过期天数
     * @return 清理的记录数
     */
    int cleanupExpiredBackups(int expireDays);
}
