package com.logictrue.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.iot.entity.DeviceDataBackupRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 设备数据备份记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Mapper
public interface DeviceDataBackupRecordMapper extends BaseMapper<DeviceDataBackupRecord> {

    /**
     * 执行备份主表数据SQL
     */
    @Update("INSERT INTO device_detection_data_backup (" +
            "original_id, device_code, template_id, template_name, file_name, file_path, file_size, " +
            "parse_status, parse_message, parse_time, total_sheets, parsed_sheets, " +
            "basic_fields_count, table_rows_count, create_by, create_time, update_by, update_time, " +
            "remark, backup_time, backup_reason) " +
            "SELECT id, device_code, template_id, template_name, file_name, file_path, file_size, " +
            "parse_status, parse_message, parse_time, total_sheets, parsed_sheets, " +
            "basic_fields_count, table_rows_count, create_by, create_time, update_by, update_time, " +
            "remark, NOW(), #{backupReason} FROM device_detection_data WHERE id = #{detectionDataId}")
    int backupMainData(@Param("backupReason") String backupReason, @Param("detectionDataId") Long detectionDataId);

    /**
     * 执行备份基础字段数据SQL
     */
    @Update("INSERT INTO device_detection_basic_field_backup (" +
            "original_id, detection_data_id, original_detection_data_id, sheet_id, sheet_name, sheet_index, " +
            "field_code, field_name, field_type, field_value, label_position, value_position, " +
            "sort_order, create_by, create_time, update_by, update_time, remark, backup_time) " +
            "SELECT id, detection_data_id, detection_data_id, sheet_id, sheet_name, sheet_index, " +
            "field_code, field_name, field_type, field_value, label_position, value_position, " +
            "sort_order, create_by, create_time, update_by, update_time, remark, NOW() " +
            "FROM device_detection_basic_field WHERE detection_data_id = #{detectionDataId}")
    int backupBasicFields(@Param("detectionDataId") Long detectionDataId);

    /**
     * 执行备份表格表头数据SQL
     */
    @Update("INSERT INTO device_detection_table_header_backup (" +
            "original_id, detection_data_id, original_detection_data_id, sheet_id, sheet_name, sheet_index, " +
            "header_code, header_name, header_position, data_type, column_order, " +
            "create_by, create_time, update_by, update_time, remark, backup_time) " +
            "SELECT id, detection_data_id, detection_data_id, sheet_id, sheet_name, sheet_index, " +
            "header_code, header_name, header_position, data_type, column_order, " +
            "create_by, create_time, update_by, update_time, remark, NOW() " +
            "FROM device_detection_table_header WHERE detection_data_id = #{detectionDataId}")
    int backupTableHeaders(@Param("detectionDataId") Long detectionDataId);

    /**
     * 执行备份表格数据SQL
     */
    @Update("INSERT INTO device_detection_table_data_backup (" +
            "original_id, detection_data_id, original_detection_data_id, sheet_id, sheet_name, sheet_index, " +
            "row_index, row_data, row_order, create_by, create_time, update_by, update_time, remark, backup_time) " +
            "SELECT id, detection_data_id, detection_data_id, sheet_id, sheet_name, sheet_index, " +
            "row_index, row_data, row_order, create_by, create_time, update_by, update_time, remark, NOW() " +
            "FROM device_detection_table_data WHERE detection_data_id = #{detectionDataId}")
    int backupTableData(@Param("detectionDataId") Long detectionDataId);

    /**
     * 执行备份解析日志数据SQL
     */
    @Update("INSERT INTO device_detection_parse_log_backup (" +
            "original_id, detection_data_id, original_detection_data_id, log_level, log_message, " +
            "sheet_id, sheet_name, position, field_code, create_by, create_time, update_by, update_time, " +
            "remark, backup_time) " +
            "SELECT id, detection_data_id, detection_data_id, log_level, log_message, " +
            "sheet_id, sheet_name, position, field_code, create_by, create_time, update_by, update_time, " +
            "remark, NOW() FROM device_detection_parse_log WHERE detection_data_id = #{detectionDataId}")
    int backupParseLogs(@Param("detectionDataId") Long detectionDataId);

    /**
     * 执行备份设备基础信息SQL
     */
    @Update("INSERT INTO drl_device_backup (" +
            "original_id, device_code, device_name, device_status, create_by, create_time, " +
            "ip, address, pdf_address, device_type, device_date, is_camera, " +
            "camera_user, camera_password, camera_ip, camera_port, analysis_type, " +
            "backup_time, backup_reason) " +
            "SELECT id, device_code, device_name, device_status, create_by, create_time, " +
            "ip, address, pdf_address, device_type, device_date, is_camera, " +
            "camera_user, camera_password, camera_ip, camera_port, analysis_type, " +
            "NOW(), #{backupReason} FROM drl_device WHERE id = #{deviceId}")
    int backupDevice(@Param("backupReason") String backupReason, @Param("deviceId") Long deviceId);

    /**
     * 执行备份Excel模板信息SQL
     */
    @Update("INSERT INTO excel_template_backup (" +
            "original_id, template_code, template_name, template_type, template_description, " +
            "template_status, template_version, create_by, create_time, update_by, update_time, " +
            "remark, backup_time, backup_reason) " +
            "SELECT id, template_code, template_name, template_type, template_description, " +
            "template_status, template_version, create_by, create_time, update_by, update_time, " +
            "remark, NOW(), #{backupReason} FROM excel_template WHERE id = #{templateId}")
    int backupTemplate(@Param("backupReason") String backupReason, @Param("templateId") Long templateId);

    /**
     * 执行备份模板单元格配置SQL
     */
    @Update("INSERT INTO excel_template_cell_backup (" +
            "original_id, template_id, original_template_id, sheet_name, cell_position, cell_type, " +
            "field_code, field_name, field_type, is_required, default_value, validation_rule, " +
            "sort_order, create_by, create_time, update_by, update_time, remark, backup_time) " +
            "SELECT id, template_id, template_id, sheet_name, cell_position, cell_type, " +
            "field_code, field_name, field_type, is_required, default_value, validation_rule, " +
            "sort_order, create_by, create_time, update_by, update_time, remark, NOW() " +
            "FROM excel_template_cell WHERE template_id = #{templateId}")
    int backupTemplateCells(@Param("templateId") Long templateId);

    /**
     * 执行备份设备模板绑定SQL
     */
    @Update("INSERT INTO device_template_binding_backup (" +
            "original_id, device_code, template_id, template_code, template_name, " +
            "binding_status, binding_priority, effective_date, expiry_date, " +
            "create_by, create_time, update_by, update_time, remark, " +
            "backup_time, backup_reason) " +
            "SELECT id, device_code, template_id, template_code, template_name, " +
            "binding_status, binding_priority, effective_date, expiry_date, " +
            "create_by, create_time, update_by, update_time, remark, " +
            "NOW(), #{backupReason} FROM device_template_binding WHERE id = #{bindingId}")
    int backupTemplateBinding(@Param("backupReason") String backupReason, @Param("bindingId") Long bindingId);
}
