package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logictrue.iot.entity.*;
import com.logictrue.iot.entity.dto.*;
import com.logictrue.iot.mapper.DeviceImportLogMapper;
import com.logictrue.iot.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 设备数据导入Service实现类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class DeviceDataImportServiceImpl extends ServiceImpl<DeviceImportLogMapper, DeviceImportLog>
        implements IDeviceDataImportService {

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IDeviceTemplateBindingService bindingService;

    @Autowired
    private IExcelTemplateService templateService;

    @Autowired
    private IDeviceDetectionDataService detectionDataService;

    @Autowired
    private IDeviceDetectionBasicFieldService basicFieldService;

    @Autowired
    private IDeviceDetectionTableHeaderService tableHeaderService;

    @Autowired
    private IDeviceDetectionTableDataService tableDataService;

    @Autowired
    private IDeviceDetectionParseLogService parseLogService;

    @Value("${file.path:/home/<USER>")
    private String uploadPath;

    @Autowired
    private ObjectMapper objectMapper;

    // 存储导入任务状态的内存缓存
    private final Map<String, DeviceImportResult> importTaskCache = new ConcurrentHashMap<>();

    // 存储解析后的导入数据
    private final Map<String, ExportedDeviceData> importDataCache = new ConcurrentHashMap<>();

    @Override
    public String uploadAndValidateFile(MultipartFile file) {
        try {
            // 生成任务ID
            String taskId = "import_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);

            // 验证文件格式
            if (!file.getOriginalFilename().toLowerCase().endsWith(".zip")) {
                throw new RuntimeException("只支持ZIP格式的导入文件");
            }

            // 保存上传文件
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String importDir = uploadPath + "/imports";
            Path importDirPath = Paths.get(importDir);
            if (!Files.exists(importDirPath)) {
                Files.createDirectories(importDirPath);
            }

            String fileName = "import_" + timestamp + "_" + taskId.substring(taskId.length() - 8) + ".zip";
            String filePath = importDir + "/" + fileName;
            file.transferTo(new File(filePath));

            // 创建导入日志记录
            DeviceImportLog importLog = new DeviceImportLog();
            importLog.setTaskId(taskId);
            importLog.setFileName(file.getOriginalFilename());
            importLog.setFilePath(filePath);
            importLog.setFileSize(file.getSize());
            importLog.setImportStatus(0); // 进行中
            importLog.setProgress(0);
            importLog.setCurrentStep("文件上传完成，开始验证");
            save(importLog);

            // 初始化导入结果
            DeviceImportResult result = new DeviceImportResult();
            result.setTaskId(taskId);
            result.setStatus(0);
            result.setProgress(0);
            result.setCurrentStep("文件上传完成，开始验证");
            result.setStartTime(LocalDateTime.now());
            importTaskCache.put(taskId, result);

            // 异步验证文件
            validateFileAsync(taskId, filePath);

            return taskId;
        } catch (Exception e) {
            log.error("上传导入文件失败", e);
            throw new RuntimeException("上传导入文件失败: " + e.getMessage());
        }
    }

    @Async
    public void validateFileAsync(String taskId, String filePath) {
        DeviceImportResult result = importTaskCache.get(taskId);
        DeviceImportLog importLog = getByTaskId(taskId);

        try {
            updateProgress(taskId, 10, "解析导入文件");
            ExportedDeviceData importData = parseImportFile(filePath);
            importDataCache.put(taskId, importData);

            updateProgress(taskId, 50, "验证数据完整性");
            validateImportDataIntegrity(importData);

            updateProgress(taskId, 80, "检查数据冲突");
            checkDataConflicts(importData, result);

            updateProgress(taskId, 100, "验证完成");
            result.setStatus(1); // 验证成功，等待导入
            result.setCurrentStep("验证完成，可以开始导入");

            // 更新统计信息
            updateImportStatistics(result, importData);

            importLog.setImportStatus(1);
            importLog.setCurrentStep("验证完成，可以开始导入");
            importLog.setProgress(100);
            updateById(importLog);

        } catch (Exception e) {
            log.error("验证导入文件失败: {}", taskId, e);
            result.setStatus(2);
            result.setErrorMessage(e.getMessage());
            result.setCurrentStep("验证失败");

            importLog.setImportStatus(2);
            importLog.setErrorMessage(e.getMessage());
            importLog.setCurrentStep("验证失败");
            updateById(importLog);
        }
    }

    @Override
    public DeviceImportResult validateImportData(String taskId) {
        return importTaskCache.get(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceImportResult executeImport(DeviceImportRequest request) {
        String taskId = request.getTaskId();
        DeviceImportResult result = importTaskCache.get(taskId);
        DeviceImportLog importLog = getByTaskId(taskId);

        if (result == null) {
            throw new RuntimeException("导入任务不存在: " + taskId);
        }

        ExportedDeviceData importData = importDataCache.get(taskId);
        if (importData == null) {
            throw new RuntimeException("导入数据不存在: " + taskId);
        }

        try {
            result.setStatus(0); // 导入中
            result.setCurrentStep("开始导入数据");
            importLog.setImportStatus(0);
            importLog.setCurrentStep("开始导入数据");
            importLog.setImportMode(request.getImportMode().name());
            importLog.setConflictStrategy(request.getConflictStrategy().name());
            importLog.setOverrideDeviceConfig(request.getOverrideDeviceConfig());
            importLog.setOverrideTemplateBinding(request.getOverrideTemplateBinding());
            importLog.setOverrideDetectionData(request.getOverrideDetectionData());
            updateById(importLog);

            // 异步执行导入
            executeImportAsync(taskId, request, importData);

            return result;
        } catch (Exception e) {
            log.error("执行导入失败: {}", taskId, e);
            result.setStatus(2);
            result.setErrorMessage(e.getMessage());
            throw new RuntimeException("执行导入失败: " + e.getMessage());
        }
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void executeImportAsync(String taskId, DeviceImportRequest request, ExportedDeviceData importData) {
        DeviceImportResult result = importTaskCache.get(taskId);
        DeviceImportLog importLog = getByTaskId(taskId);

        List<String> successDeviceCodes = new ArrayList<>();
        List<String> failedDeviceCodes = new ArrayList<>();
        List<DeviceImportResult.ImportDetail> importDetails = new ArrayList<>();

        try {
            // 步骤1: 导入设备基础信息
            updateProgress(taskId, 20, "导入设备基础信息");
            importDeviceInfo(importData.getDevices(), request, successDeviceCodes, failedDeviceCodes, importDetails);

            // 步骤2: 导入Excel模板信息
            if (request.getOverrideTemplateBinding() && importData.getExcelTemplates() != null) {
                updateProgress(taskId, 40, "导入Excel模板信息");
                importExcelTemplates(importData.getExcelTemplates(), request, importDetails);
            }

            // 步骤3: 导入设备模板绑定
            if (request.getOverrideTemplateBinding() && importData.getTemplateBindings() != null) {
                updateProgress(taskId, 60, "导入设备模板绑定");
                importTemplateBindings(importData.getTemplateBindings(), request, importDetails);
            }

            // 步骤4: 导入检测数据
            if (request.getOverrideDetectionData() && importData.getDetectionData() != null) {
                updateProgress(taskId, 80, "导入检测数据");
                importDetectionData(importData.getDetectionData(), request, importDetails);
            }

            // 步骤5: 完成导入
            updateProgress(taskId, 100, "导入完成");
            completeImport(taskId, successDeviceCodes, failedDeviceCodes, importDetails, result, importLog);

        } catch (Exception e) {
            log.error("导入任务执行失败: {}", taskId, e);
            failImport(taskId, e.getMessage(), result, importLog);
        }
    }

    @Override
    public DeviceImportResult getImportProgress(String taskId) {
        return importTaskCache.get(taskId);
    }

    @Override
    public boolean cancelImport(String taskId) {
        importTaskCache.remove(taskId);
        importDataCache.remove(taskId);

        DeviceImportLog importLog = getByTaskId(taskId);
        if (importLog != null) {
            importLog.setImportStatus(2);
            importLog.setErrorMessage("用户取消导入");
            importLog.setCompleteTime(LocalDateTime.now());
            updateById(importLog);
        }

        return true;
    }

    @Override
    public ExportedDeviceData parseImportFile(String filePath) {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(filePath))) {
            ExportedDeviceData importData = new ExportedDeviceData();
            Map<String, String> rawFileEntries = new HashMap<>(); // 存储原始文件条目
            ZipEntry entry;

            // 第一遍：解析JSON文件
            while ((entry = zis.getNextEntry()) != null) {
                String entryName = entry.getName();

                if (entryName.equals("metadata.json")) {
                    String content = readZipEntryContent(zis);
                    ExportedDeviceData.ExportMetadata metadata = objectMapper.readValue(content, ExportedDeviceData.ExportMetadata.class);
                    importData.setMetadata(metadata);

                } else if (entryName.equals("devices/device_info.json")) {
                    String content = readZipEntryContent(zis);
                    List<ExportedDeviceData.DeviceInfo> devices = objectMapper.readValue(content,
                        objectMapper.getTypeFactory().constructCollectionType(List.class, ExportedDeviceData.DeviceInfo.class));
                    importData.setDevices(devices);

                } else if (entryName.equals("devices/device_templates.json")) {
                    String content = readZipEntryContent(zis);
                    List<ExportedDeviceData.DeviceTemplateBindingInfo> bindings = objectMapper.readValue(content,
                        objectMapper.getTypeFactory().constructCollectionType(List.class, ExportedDeviceData.DeviceTemplateBindingInfo.class));
                    importData.setTemplateBindings(bindings);

                } else if (entryName.equals("templates/excel_templates.json")) {
                    String content = readZipEntryContent(zis);
                    List<ExportedDeviceData.ExcelTemplateInfo> templates = objectMapper.readValue(content,
                        objectMapper.getTypeFactory().constructCollectionType(List.class, ExportedDeviceData.ExcelTemplateInfo.class));
                    importData.setExcelTemplates(templates);

                } else if (entryName.equals("detection_data/parsed_data.json")) {
                    String content = readZipEntryContent(zis);
                    List<ExportedDeviceData.DetectionDataInfo> detectionData = objectMapper.readValue(content,
                        objectMapper.getTypeFactory().constructCollectionType(List.class, ExportedDeviceData.DetectionDataInfo.class));
                    importData.setDetectionData(detectionData);

                    // 统计检测数据信息
                    long mainDataCount = detectionData.stream()
                        .filter(data -> data.getMainData() != null)
                        .count();
                    long hasRawFileCount = detectionData.stream()
                        .filter(data -> data.getRawFileInfo() != null)
                        .count();
                    log.info("解析检测数据 - 总条数: {}, 主表数据: {}, 包含原始文件: {}",
                             detectionData.size(), mainDataCount, hasRawFileCount);

                } else if (entryName.equals("detection_data/main_data.json")) {
                    String content = readZipEntryContent(zis);
                    List<DeviceDetectionData> mainDataList = objectMapper.readValue(content,
                        objectMapper.getTypeFactory().constructCollectionType(List.class, DeviceDetectionData.class));
                    log.info("解析主表数据 - 条数: {}", mainDataList.size());
                    // 主表数据用于验证，实际导入使用parsed_data.json中的数据

                } else if (entryName.startsWith("raw_files/")) {
                    // 记录原始文件条目，稍后处理
                    rawFileEntries.put(entryName, filePath);
                }

                zis.closeEntry();
            }

            // 第二遍：处理原始文件（如果需要导入原始文件）
            if (!rawFileEntries.isEmpty()) {
                extractRawFiles(filePath, rawFileEntries, importData);
            }

            return importData;
        } catch (Exception e) {
            log.error("解析导入文件失败: {}", filePath, e);
            throw new RuntimeException("解析导入文件失败: " + e.getMessage());
        }
    }

    /**
     * 提取原始文件到对应的设备目录
     */
    private void extractRawFiles(String zipFilePath, Map<String, String> rawFileEntries, ExportedDeviceData importData) {
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry;

            while ((entry = zis.getNextEntry()) != null) {
                String entryName = entry.getName();

                if (entryName.startsWith("raw_files/") && rawFileEntries.containsKey(entryName)) {
                    // 解析路径: raw_files/设备编码/日期/文件名
                    String[] pathParts = entryName.split("/");
                    if (pathParts.length >= 3) {
                        String deviceCode = pathParts[1];
                        String fileName = pathParts[pathParts.length - 1];
                        String dateFolder = pathParts.length > 3 ? pathParts[2] : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

                        // 创建目标目录
                        Path targetDir = Paths.get(uploadPath, deviceCode, dateFolder);
                        if (!Files.exists(targetDir)) {
                            Files.createDirectories(targetDir);
                        }

                        // 提取文件
                        Path targetFile = targetDir.resolve(fileName);
                        try (FileOutputStream fos = new FileOutputStream(targetFile.toFile())) {
                            byte[] buffer = new byte[1024];
                            int length;
                            while ((length = zis.read(buffer)) > 0) {
                                fos.write(buffer, 0, length);
                            }
                        }

                        log.info("成功提取原始文件: {} -> {}", entryName, targetFile);

                        // 更新检测数据中的文件路径
                        updateDetectionDataFilePath(importData, deviceCode, fileName, targetFile.toString());
                    }
                }

                zis.closeEntry();
            }
        } catch (Exception e) {
            log.error("提取原始文件失败: {}", zipFilePath, e);
        }
    }

    /**
     * 更新检测数据中的文件路径
     */
    private void updateDetectionDataFilePath(ExportedDeviceData importData, String deviceCode, String fileName, String newFilePath) {
        if (importData.getDetectionData() != null) {
            for (ExportedDeviceData.DetectionDataInfo dataInfo : importData.getDetectionData()) {
                if (deviceCode.equals(dataInfo.getDeviceCode()) &&
                    dataInfo.getRawFileInfo() != null &&
                    fileName.equals(dataInfo.getRawFileInfo().getFileName())) {
                    dataInfo.getRawFileInfo().setFilePath(newFilePath);
                    break;
                }
            }
        }
    }

    @Override
    public int cleanupTempImportFiles(int expireDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(expireDays);

        LambdaQueryWrapper<DeviceImportLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(DeviceImportLog::getCreateTime, expireTime);
        wrapper.isNotNull(DeviceImportLog::getFilePath);

        List<DeviceImportLog> expiredLogs = list(wrapper);
        int cleanedCount = 0;

        for (DeviceImportLog deviceImportLog : expiredLogs) {
            try {
                Path filePath = Paths.get(deviceImportLog.getFilePath());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    cleanedCount++;
                }
            } catch (Exception e) {
                log.warn("清理临时导入文件失败: {}", deviceImportLog.getFilePath(), e);
            }
        }

        return cleanedCount;
    }

    // 辅助方法
    private DeviceImportLog getByTaskId(String taskId) {
        LambdaQueryWrapper<DeviceImportLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceImportLog::getTaskId, taskId);
        return getOne(wrapper);
    }

    private void updateProgress(String taskId, int progress, String step) {
        DeviceImportResult result = importTaskCache.get(taskId);
        if (result != null) {
            result.setProgress(progress);
            result.setCurrentStep(step);
        }

        DeviceImportLog importLog = getByTaskId(taskId);
        if (importLog != null) {
            importLog.setProgress(progress);
            importLog.setCurrentStep(step);
            updateById(importLog);
        }

        log.info("导入任务 {} 进度更新: {}% - {}", taskId, progress, step);
    }

    private String readZipEntryContent(ZipInputStream zis) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = zis.read(buffer)) > 0) {
            baos.write(buffer, 0, len);
        }
        return baos.toString("UTF-8");
    }

    private void validateImportDataIntegrity(ExportedDeviceData importData) {
        if (importData.getMetadata() == null) {
            throw new RuntimeException("导入文件缺少元数据信息");
        }

        if (importData.getDevices() == null || importData.getDevices().isEmpty()) {
            throw new RuntimeException("导入文件中没有设备信息");
        }

        // 验证版本兼容性
        String version = importData.getMetadata().getVersion();
        if (!"1.0".equals(version)) {
            throw new RuntimeException("不支持的导入文件版本: " + version);
        }

        // 验证设备编码不为空
        for (ExportedDeviceData.DeviceInfo device : importData.getDevices()) {
            if (!StringUtils.hasText(device.getDeviceCode())) {
                throw new RuntimeException("设备编码不能为空");
            }
        }
    }

    private void checkDataConflicts(ExportedDeviceData importData, DeviceImportResult result) {
        List<String> conflictDevices = new ArrayList<>();

        for (ExportedDeviceData.DeviceInfo deviceInfo : importData.getDevices()) {
            if (deviceService.existsByDeviceCode(deviceInfo.getDeviceCode())) {
                conflictDevices.add(deviceInfo.getDeviceCode());
            }
        }

        if (!conflictDevices.isEmpty()) {
            log.info("发现设备编码冲突: {}", conflictDevices);
            // 这里可以根据需要设置冲突信息到result中
        }
    }

    private void updateImportStatistics(DeviceImportResult result, ExportedDeviceData importData) {
        DeviceImportResult.ImportStatistics statistics = new DeviceImportResult.ImportStatistics();

        statistics.setTotalDeviceCount(importData.getDevices() != null ? importData.getDevices().size() : 0);
        statistics.setDeviceInfoImportCount(statistics.getTotalDeviceCount());

        if (importData.getTemplateBindings() != null) {
            statistics.setTemplateBindingImportCount(importData.getTemplateBindings().size());
        }

        if (importData.getDetectionData() != null) {
            long basicFieldCount = importData.getDetectionData().stream()
                .mapToLong(data -> data.getBasicFields() != null ? data.getBasicFields().size() : 0)
                .sum();
            statistics.setBasicFieldImportCount(basicFieldCount);

            long tableDataRowCount = importData.getDetectionData().stream()
                .mapToLong(data -> data.getTableDataList() != null ? data.getTableDataList().size() : 0)
                .sum();
            statistics.setTableDataRowImportCount(tableDataRowCount);
        }

        result.setStatistics(statistics);
    }

    private void importDeviceInfo(List<ExportedDeviceData.DeviceInfo> deviceInfoList,
                                DeviceImportRequest request,
                                List<String> successDeviceCodes,
                                List<String> failedDeviceCodes,
                                List<DeviceImportResult.ImportDetail> importDetails) {

        for (ExportedDeviceData.DeviceInfo deviceInfo : deviceInfoList) {
            try {
                Device existingDevice = deviceService.getByDeviceCode(deviceInfo.getDeviceCode());

                if (existingDevice != null) {
                    // 处理冲突
                    if (request.getConflictStrategy() == DeviceImportRequest.ConflictStrategy.SKIP) {
                        addImportDetail(importDetails, deviceInfo.getDeviceCode(), "SKIPPED",
                            "设备已存在，跳过导入", "DEVICE_INFO");
                        continue;
                    } else if (request.getConflictStrategy() == DeviceImportRequest.ConflictStrategy.ERROR) {
                        throw new RuntimeException("设备编码已存在: " + deviceInfo.getDeviceCode());
                    }
                    // OVERRIDE策略继续执行更新
                }

                Device device = new Device();
                BeanUtils.copyProperties(deviceInfo, device);

                if (existingDevice != null) {
                    device.setId(existingDevice.getId());
                    deviceService.updateById(device);
                    addImportDetail(importDetails, deviceInfo.getDeviceCode(), "SUCCESS",
                        "设备信息更新成功", "DEVICE_INFO");
                } else {
                    deviceService.save(device);
                    addImportDetail(importDetails, deviceInfo.getDeviceCode(), "SUCCESS",
                        "设备信息导入成功", "DEVICE_INFO");
                }

                successDeviceCodes.add(deviceInfo.getDeviceCode());

            } catch (Exception e) {
                log.error("导入设备信息失败: {}", deviceInfo.getDeviceCode(), e);
                failedDeviceCodes.add(deviceInfo.getDeviceCode());
                addImportDetail(importDetails, deviceInfo.getDeviceCode(), "FAILED",
                    "导入失败: " + e.getMessage(), "DEVICE_INFO");
            }
        }
    }

    private void importExcelTemplates(List<ExportedDeviceData.ExcelTemplateInfo> templateInfoList,
                                    DeviceImportRequest request,
                                    List<DeviceImportResult.ImportDetail> importDetails) {

        for (ExportedDeviceData.ExcelTemplateInfo templateInfo : templateInfoList) {
            try {
                LambdaQueryWrapper<ExcelTemplate> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ExcelTemplate::getTemplateCode, templateInfo.getTemplateCode());
                ExcelTemplate existingTemplate = templateService.getOne(wrapper);

                ExcelTemplate template = new ExcelTemplate();
                BeanUtils.copyProperties(templateInfo, template);

                if (existingTemplate != null) {
                    if (request.getConflictStrategy() == DeviceImportRequest.ConflictStrategy.SKIP) {
                        continue;
                    }
                    template.setId(existingTemplate.getId());
                    templateService.updateById(template);
                } else {
                    templateService.save(template);
                }

                addImportDetail(importDetails, templateInfo.getTemplateCode(), "SUCCESS",
                    "Excel模板导入成功", "EXCEL_TEMPLATE");

            } catch (Exception e) {
                log.error("导入Excel模板失败: {}", templateInfo.getTemplateCode(), e);
                addImportDetail(importDetails, templateInfo.getTemplateCode(), "FAILED",
                    "导入失败: " + e.getMessage(), "EXCEL_TEMPLATE");
            }
        }
    }

    private void importTemplateBindings(List<ExportedDeviceData.DeviceTemplateBindingInfo> bindingInfoList,
                                      DeviceImportRequest request,
                                      List<DeviceImportResult.ImportDetail> importDetails) {

        for (ExportedDeviceData.DeviceTemplateBindingInfo bindingInfo : bindingInfoList) {
            try {
                DeviceTemplateBinding existingBinding = bindingService.getBindingByDeviceCode(bindingInfo.getDeviceCode());

                DeviceTemplateBinding binding = new DeviceTemplateBinding();
                BeanUtils.copyProperties(bindingInfo, binding);

                if (existingBinding != null) {
                    if (request.getConflictStrategy() == DeviceImportRequest.ConflictStrategy.SKIP) {
                        continue;
                    }
                    binding.setId(existingBinding.getId());
                    bindingService.updateById(binding);
                } else {
                    bindingService.save(binding);
                }

                addImportDetail(importDetails, bindingInfo.getDeviceCode(), "SUCCESS",
                    "设备模板绑定导入成功", "TEMPLATE_BINDING");

            } catch (Exception e) {
                log.error("导入设备模板绑定失败: {}", bindingInfo.getDeviceCode(), e);
                addImportDetail(importDetails, bindingInfo.getDeviceCode(), "FAILED",
                    "导入失败: " + e.getMessage(), "TEMPLATE_BINDING");
            }
        }
    }

    private void importDetectionData(List<ExportedDeviceData.DetectionDataInfo> detectionDataList,
                                   DeviceImportRequest request,
                                   List<DeviceImportResult.ImportDetail> importDetails) {

        log.info("开始导入检测数据 - 总条数: {}, 冲突策略: {}",
                 detectionDataList.size(), request.getConflictStrategy());

        int successCount = 0;
        int skipCount = 0;
        int errorCount = 0;

        for (ExportedDeviceData.DetectionDataInfo dataInfo : detectionDataList) {
            try {
                DeviceDetectionData mainData = dataInfo.getMainData();

                // 检查是否存在相同的检测数据
                LambdaQueryWrapper<DeviceDetectionData> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(DeviceDetectionData::getDeviceCode, mainData.getDeviceCode())
                       .eq(DeviceDetectionData::getFileName, mainData.getFileName())
                       .eq(DeviceDetectionData::getCreateTime, mainData.getCreateTime());
                DeviceDetectionData existingData = detectionDataService.getOne(wrapper);

                if (existingData != null) {
                    if (request.getConflictStrategy() == DeviceImportRequest.ConflictStrategy.SKIP) {
                        skipCount++;
                        log.info("跳过已存在的检测数据 - 设备: {}, 文件: {}",
                                 mainData.getDeviceCode(), mainData.getFileName());
                        continue;
                    } else if (request.getConflictStrategy() == DeviceImportRequest.ConflictStrategy.ERROR) {
                        errorCount++;
                        throw new RuntimeException("检测数据已存在: " + mainData.getFileName());
                    }
                    // 删除现有数据
                    log.info("覆盖已存在的检测数据 - 设备: {}, 文件: {}",
                             mainData.getDeviceCode(), mainData.getFileName());
                    deleteExistingDetectionData(existingData.getId());
                }

                // 保存主数据
                mainData.setId(null); // 重置ID
                detectionDataService.save(mainData);
                Long newDataId = mainData.getId();

                // 保存基础字段
                if (dataInfo.getBasicFields() != null) {
                    for (DeviceDetectionBasicField field : dataInfo.getBasicFields()) {
                        field.setId(null);
                        field.setDetectionDataId(newDataId);
                        basicFieldService.save(field);
                    }
                }

                // 保存表格表头
                if (dataInfo.getTableHeaders() != null) {
                    for (DeviceDetectionTableHeader header : dataInfo.getTableHeaders()) {
                        header.setId(null);
                        header.setDetectionDataId(newDataId);
                        tableHeaderService.save(header);
                    }
                }

                // 保存表格数据
                if (dataInfo.getTableDataList() != null) {
                    for (DeviceDetectionTableData tableData : dataInfo.getTableDataList()) {
                        tableData.setId(null);
                        tableData.setDetectionDataId(newDataId);
                        tableDataService.save(tableData);
                    }
                }

                // 保存解析日志
                if (dataInfo.getParseLogs() != null) {
                    for (DeviceDetectionParseLog parseLog : dataInfo.getParseLogs()) {
                        parseLog.setId(null);
                        parseLog.setDetectionDataId(newDataId);
                        parseLogService.save(parseLog);
                    }
                }

                successCount++;
                addImportDetail(importDetails, mainData.getDeviceCode(), "SUCCESS",
                    "检测数据导入成功: " + mainData.getFileName(), "DETECTION_DATA");

                log.info("成功导入检测数据 - 设备: {}, 文件: {}, 主表ID: {}",
                         mainData.getDeviceCode(), mainData.getFileName(), newDataId);

            } catch (Exception e) {
                errorCount++;
                log.error("导入检测数据失败: {}", dataInfo.getMainData().getFileName(), e);
                addImportDetail(importDetails, dataInfo.getMainData().getDeviceCode(), "FAILED",
                    "导入失败: " + e.getMessage(), "DETECTION_DATA");
            }
        }

        log.info("检测数据导入完成 - 总条数: {}, 成功: {}, 跳过: {}, 错误: {}",
                 detectionDataList.size(), successCount, skipCount, errorCount);
    }

    private void deleteExistingDetectionData(Long detectionDataId) {
        // 删除基础字段
        LambdaQueryWrapper<DeviceDetectionBasicField> basicWrapper = new LambdaQueryWrapper<>();
        basicWrapper.eq(DeviceDetectionBasicField::getDetectionDataId, detectionDataId);
        basicFieldService.remove(basicWrapper);

        // 删除表格表头
        LambdaQueryWrapper<DeviceDetectionTableHeader> headerWrapper = new LambdaQueryWrapper<>();
        headerWrapper.eq(DeviceDetectionTableHeader::getDetectionDataId, detectionDataId);
        tableHeaderService.remove(headerWrapper);

        // 删除表格数据
        LambdaQueryWrapper<DeviceDetectionTableData> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(DeviceDetectionTableData::getDetectionDataId, detectionDataId);
        tableDataService.remove(dataWrapper);

        // 删除解析日志
        LambdaQueryWrapper<DeviceDetectionParseLog> logWrapper = new LambdaQueryWrapper<>();
        logWrapper.eq(DeviceDetectionParseLog::getDetectionDataId, detectionDataId);
        parseLogService.remove(logWrapper);

        // 删除主数据
        detectionDataService.removeById(detectionDataId);
    }

    private void addImportDetail(List<DeviceImportResult.ImportDetail> importDetails,
                               String deviceCode, String status, String message, String dataType) {
        DeviceImportResult.ImportDetail detail = new DeviceImportResult.ImportDetail();
        detail.setDeviceCode(deviceCode);
        detail.setStatus(status);
        detail.setMessage(message);
        detail.setDataType(dataType);
        detail.setProcessTime(LocalDateTime.now());
        importDetails.add(detail);
    }

    private void completeImport(String taskId,
                              List<String> successDeviceCodes,
                              List<String> failedDeviceCodes,
                              List<DeviceImportResult.ImportDetail> importDetails,
                              DeviceImportResult result,
                              DeviceImportLog importLog) {
        try {
            result.setImportedDeviceCount(successDeviceCodes.size());
            result.setFailedDeviceCount(failedDeviceCodes.size());
            result.setSuccessDeviceCodes(successDeviceCodes);
            result.setFailedDeviceCodes(failedDeviceCodes);
            result.setImportDetails(importDetails);
            result.setCompleteTime(LocalDateTime.now());

            // 计算成功率
            int totalCount = successDeviceCodes.size() + failedDeviceCodes.size();
            if (totalCount > 0) {
                double successRate = (double) successDeviceCodes.size() / totalCount * 100;
                if (result.getStatistics() != null) {
                    result.getStatistics().setSuccessRate(successRate);
                }
            }

            // 确定最终状态
            if (failedDeviceCodes.isEmpty()) {
                result.setStatus(1); // 完全成功
            } else if (successDeviceCodes.isEmpty()) {
                result.setStatus(2); // 完全失败
            } else {
                result.setStatus(3); // 部分成功
            }

            // 更新数据库
            try {
                importLog.setImportStatus(result.getStatus());
                importLog.setImportedDeviceCount(successDeviceCodes.size());
                importLog.setFailedDeviceCount(failedDeviceCodes.size());
                importLog.setSuccessDeviceCodes(objectMapper.writeValueAsString(successDeviceCodes));
                importLog.setFailedDeviceCodes(objectMapper.writeValueAsString(failedDeviceCodes));
                importLog.setCompleteTime(LocalDateTime.now());
                updateById(importLog);
            } catch (Exception e) {
                log.error("更新导入日志失败: {}", taskId, e);
            }

            log.info("导入任务完成: {}, 成功: {}, 失败: {}", taskId, successDeviceCodes.size(), failedDeviceCodes.size());

        } catch (Exception e) {
            log.error("完成导入任务时发生错误: {}", taskId, e);
            failImport(taskId, "完成导入时发生错误: " + e.getMessage(), result, importLog);
        }
    }

    private void failImport(String taskId, String errorMessage, DeviceImportResult result, DeviceImportLog importLog) {
        result.setStatus(2); // 失败
        result.setCompleteTime(LocalDateTime.now());
        result.setErrorMessage(errorMessage);

        importLog.setImportStatus(2);
        importLog.setErrorMessage(errorMessage);
        importLog.setCompleteTime(LocalDateTime.now());
        updateById(importLog);

        log.error("导入任务失败: {}, 错误信息: {}", taskId, errorMessage);
    }

    @Override
    public Page<DeviceImportLog> getImportHistory(int pageNum, int pageSize, String taskId, Integer importStatus, String startTime, String endTime) {
        Page<DeviceImportLog> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<DeviceImportLog> wrapper = new LambdaQueryWrapper<>();

        // 任务ID模糊查询
        if (StringUtils.hasText(taskId)) {
            wrapper.like(DeviceImportLog::getTaskId, taskId);
        }

        // 导入状态筛选
        if (importStatus != null) {
            wrapper.eq(DeviceImportLog::getImportStatus, importStatus);
        }

        // 时间范围筛选
        if (StringUtils.hasText(startTime)) {
            try {
                LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                wrapper.ge(DeviceImportLog::getCreateTime, start);
            } catch (Exception e) {
                log.warn("解析开始时间失败: {}", startTime, e);
            }
        }

        if (StringUtils.hasText(endTime)) {
            try {
                LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                wrapper.le(DeviceImportLog::getCreateTime, end);
            } catch (Exception e) {
                log.warn("解析结束时间失败: {}", endTime, e);
            }
        }

        // 按创建时间倒序排列
        wrapper.orderByDesc(DeviceImportLog::getCreateTime);

        return page(page, wrapper);
    }
}
