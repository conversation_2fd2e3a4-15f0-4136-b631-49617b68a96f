package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logictrue.iot.entity.*;
import com.logictrue.iot.entity.dto.*;
import com.logictrue.iot.mapper.*;
import com.logictrue.iot.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 设备数据导出Service实现类
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@Service
public class DeviceDataExportServiceImpl extends ServiceImpl<DeviceExportLogMapper, DeviceExportLog>
        implements IDeviceDataExportService {

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IDeviceTemplateBindingService bindingService;

    @Autowired
    private IExcelTemplateService templateService;

    @Autowired
    private IDeviceDetectionDataService detectionDataService;

    @Autowired
    private IDeviceDetectionBasicFieldService basicFieldService;

    @Autowired
    private IDeviceDetectionTableHeaderService tableHeaderService;

    @Autowired
    private IDeviceDetectionTableDataService tableDataService;

    @Autowired
    private IDeviceDetectionParseLogService parseLogService;

    @Value("${file.path:/home/<USER>")
    private String uploadPath;

    @Value("${file.domain:127.0.0.1}")
    private String domain;

    @Value("${file.prefix:/static}")
    private String filePrefix;

    @Autowired
    private ObjectMapper objectMapper;

    // 存储导出任务状态的内存缓存
    private final Map<String, DeviceExportResult> exportTaskCache = new ConcurrentHashMap<>();

    @Override
    public String startExport(DeviceExportRequest request) {
        log.info("收到导出请求 - 设备: {}, 时间范围: {}, 包含原始文件: {}, 包含解析数据: {}, 包含模板信息: {}",
                 request.getDeviceCodes(), request.getTimeRangeType(),
                 request.getIncludeRawFiles(), request.getIncludeParsedData(), request.getIncludeTemplateInfo());

        try {
            // 处理设备编码列表
            List<String> actualDeviceCodes;
            if (request.isAllDevices()) {
                // 获取所有设备编码
                actualDeviceCodes = deviceService.getAllDeviceCodes();
                if (actualDeviceCodes.isEmpty()) {
                    throw new RuntimeException("没有找到任何设备");
                }
                log.info("导出全部设备，共 {} 个设备", actualDeviceCodes.size());
            } else {
                actualDeviceCodes = request.getActualDeviceCodes();
                if (actualDeviceCodes == null || actualDeviceCodes.isEmpty()) {
                    throw new RuntimeException("设备编码列表不能为空");
                }
            }

            // 生成任务ID
            String taskId = "export_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
            log.info("生成导出任务ID: {}, 实际设备数量: {}", taskId, actualDeviceCodes.size());

            // 创建导出日志记录
            DeviceExportLog exportLog = new DeviceExportLog();
            exportLog.setTaskId(taskId);
            exportLog.setDeviceCodes(objectMapper.writeValueAsString(actualDeviceCodes));
            exportLog.setTimeRangeStart(request.getActualStartTime());
            exportLog.setTimeRangeEnd(request.getActualEndTime());
            exportLog.setExportStatus(0); // 进行中
            exportLog.setProgress(0);
            exportLog.setCurrentStep("初始化导出任务");
            exportLog.setIncludeRawFiles(request.getIncludeRawFiles());
            exportLog.setIncludeParsedData(request.getIncludeParsedData());
            exportLog.setIncludeTemplateInfo(request.getIncludeTemplateInfo());
            exportLog.setRemark(request.getRemark());
            save(exportLog);

            // 初始化导出结果
            DeviceExportResult result = new DeviceExportResult();
            result.setTaskId(taskId);
            result.setStatus(0);
            result.setProgress(0);
            result.setCurrentStep("初始化导出任务");
            result.setStartTime(LocalDateTime.now());
            result.setExportedDeviceCodes(actualDeviceCodes);
            exportTaskCache.put(taskId, result);

            // 创建修改后的请求对象用于后续处理
            DeviceExportRequest processedRequest = createProcessedRequest(request, actualDeviceCodes);

            // 异步执行导出
            executeExportAsync(taskId, processedRequest);

            return taskId;
        } catch (Exception e) {
            log.error("启动导出任务失败", e);
            throw new RuntimeException("启动导出任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建处理后的请求对象
     */
    private DeviceExportRequest createProcessedRequest(DeviceExportRequest original, List<String> actualDeviceCodes) {
        DeviceExportRequest processed = new DeviceExportRequest();
        processed.setDeviceCodes(actualDeviceCodes);
        processed.setTimeRangeType(original.getTimeRangeType());
        processed.setStartTime(original.getStartTime());
        processed.setEndTime(original.getEndTime());
        processed.setIncludeRawFiles(original.getIncludeRawFiles());
        processed.setIncludeParsedData(original.getIncludeParsedData());
        processed.setIncludeTemplateInfo(original.getIncludeTemplateInfo());
        processed.setRemark(original.getRemark());
        return processed;
    }

    @Async
    public void executeExportAsync(String taskId, DeviceExportRequest request) {
        DeviceExportResult result = exportTaskCache.get(taskId);
        DeviceExportLog exportLog = getByTaskId(taskId);

        try {
            List<String> deviceCodes = request.getActualDeviceCodes();

            // 步骤1: 收集设备基础信息
            updateProgress(taskId, 10, "收集设备基础信息");
            List<Device> devices = collectDeviceInfo(deviceCodes);
            result.setDeviceCount(devices.size());

            // 步骤2: 收集设备模板绑定信息
            updateProgress(taskId, 20, "收集设备模板绑定信息");
            List<DeviceTemplateBinding> bindings = collectTemplateBindings(deviceCodes);

            // 步骤3: 收集Excel模板信息
            updateProgress(taskId, 30, "收集Excel模板信息");
            List<ExcelTemplate> templates = collectExcelTemplates(bindings);

            // 步骤4: 收集检测数据
            updateProgress(taskId, 40, "收集检测数据");
            List<DeviceDetectionData> detectionDataList = collectDetectionData(
                deviceCodes,
                request.getActualStartTime(),
                request.getActualEndTime()
            );
            result.setDetectionDataCount((long) detectionDataList.size());

            // 步骤5: 收集详细检测数据
            updateProgress(taskId, 60, "收集详细检测数据");
            Map<Long, ExportedDeviceData.DetectionDataInfo> detectionDataMap =
                collectDetailedDetectionData(detectionDataList, request);

            // 步骤6: 创建导出数据结构
            updateProgress(taskId, 80, "生成导出文件");
            ExportedDeviceData exportData = createExportData(
                devices, bindings, templates, detectionDataMap, request
            );

            // 步骤7: 生成ZIP文件
            updateProgress(taskId, 90, "压缩导出文件");
            String zipFilePath = generateZipFile(taskId, exportData, request);

            // 步骤8: 完成导出
            updateProgress(taskId, 100, "导出完成");
            completeExport(taskId, zipFilePath, result, exportLog);

        } catch (Exception e) {
            log.error("导出任务执行失败: {}", taskId, e);
            failExport(taskId, e.getMessage(), result, exportLog);
        }
    }

    private List<Device> collectDeviceInfo(List<String> deviceCodes) {
        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Device::getDeviceCode, deviceCodes);
        return deviceService.list(wrapper);
    }

    private List<DeviceTemplateBinding> collectTemplateBindings(List<String> deviceCodes) {
        LambdaQueryWrapper<DeviceTemplateBinding> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DeviceTemplateBinding::getDeviceCode, deviceCodes);
        return bindingService.list(wrapper);
    }

    private List<ExcelTemplate> collectExcelTemplates(List<DeviceTemplateBinding> bindings) {
        if (bindings.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> templateIds = bindings.stream()
            .map(DeviceTemplateBinding::getTemplateId)
            .distinct()
            .collect(Collectors.toList());

        LambdaQueryWrapper<ExcelTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ExcelTemplate::getId, templateIds);
        return templateService.list(wrapper);
    }

    private List<DeviceDetectionData> collectDetectionData(List<String> deviceCodes,
                                                         LocalDateTime startTime,
                                                         LocalDateTime endTime) {
        LambdaQueryWrapper<DeviceDetectionData> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DeviceDetectionData::getDeviceCode, deviceCodes);

        if (startTime != null) {
            wrapper.ge(DeviceDetectionData::getCreateTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(DeviceDetectionData::getCreateTime, endTime);
        }

        wrapper.orderByDesc(DeviceDetectionData::getCreateTime);
        List<DeviceDetectionData> detectionDataList = detectionDataService.list(wrapper);

        log.info("查询到检测数据 - 设备数量: {}, 检测数据条数: {}, 时间范围: {} 到 {}",
                 deviceCodes.size(), detectionDataList.size(), startTime, endTime);

        // 统计有文件路径的数据
        long hasFilePathCount = detectionDataList.stream()
                .filter(data -> StringUtils.hasText(data.getFilePath()))
                .count();
        log.info("检测数据文件路径统计 - 总条数: {}, 有文件路径: {}, 无文件路径: {}",
                 detectionDataList.size(), hasFilePathCount, detectionDataList.size() - hasFilePathCount);

        return detectionDataList;
    }

    private Map<Long, ExportedDeviceData.DetectionDataInfo> collectDetailedDetectionData(
            List<DeviceDetectionData> detectionDataList, DeviceExportRequest request) {

        log.info("开始收集详细检测数据 - 数据条数: {}, 包含原始文件: {}",
                 detectionDataList.size(), request.getIncludeRawFiles());

        Map<Long, ExportedDeviceData.DetectionDataInfo> result = new HashMap<>();

        for (DeviceDetectionData data : detectionDataList) {
            ExportedDeviceData.DetectionDataInfo info = new ExportedDeviceData.DetectionDataInfo();
            info.setMainData(data);

            if (request.getIncludeParsedData()) {
                // 收集基础字段
                LambdaQueryWrapper<DeviceDetectionBasicField> basicWrapper = new LambdaQueryWrapper<>();
                basicWrapper.eq(DeviceDetectionBasicField::getDetectionDataId, data.getId());
                info.setBasicFields(basicFieldService.list(basicWrapper));

                // 收集表格表头
                LambdaQueryWrapper<DeviceDetectionTableHeader> headerWrapper = new LambdaQueryWrapper<>();
                headerWrapper.eq(DeviceDetectionTableHeader::getDetectionDataId, data.getId());
                info.setTableHeaders(tableHeaderService.list(headerWrapper));

                // 收集表格数据
                LambdaQueryWrapper<DeviceDetectionTableData> dataWrapper = new LambdaQueryWrapper<>();
                dataWrapper.eq(DeviceDetectionTableData::getDetectionDataId, data.getId());
                info.setTableDataList(tableDataService.list(dataWrapper));

                // 收集解析日志
                LambdaQueryWrapper<DeviceDetectionParseLog> logWrapper = new LambdaQueryWrapper<>();
                logWrapper.eq(DeviceDetectionParseLog::getDetectionDataId, data.getId());
                info.setParseLogs(parseLogService.list(logWrapper));
            }

            // 处理原始文件信息
            log.info("检查原始文件 - 设备: {}, 数据ID: {}, includeRawFiles: {}, filePath: {}, fileName: {}",
                     data.getDeviceCode(), data.getId(), request.getIncludeRawFiles(),
                     data.getFilePath(), data.getFileName());

            if (request.getIncludeRawFiles()) {
                if (StringUtils.hasText(data.getFilePath())) {
                    // 检查文件是否实际存在
                    Path sourceFile = Paths.get(data.getFilePath());
                    boolean fileExists = Files.exists(sourceFile);
                    log.info("原始文件检查 - 设备: {}, 文件路径: {}, 文件存在: {}, 文件大小: {}",
                             data.getDeviceCode(), data.getFilePath(), fileExists, data.getFileSize());

                    if (fileExists) {
                        try {
                            long actualFileSize = Files.size(sourceFile);
                            log.info("文件详细信息 - 设备: {}, 数据库记录大小: {}, 实际文件大小: {}",
                                     data.getDeviceCode(), data.getFileSize(), actualFileSize);

                            ExportedDeviceData.RawFileInfo rawFileInfo = new ExportedDeviceData.RawFileInfo();
                            rawFileInfo.setFileName(data.getFileName());
                            rawFileInfo.setFilePath(data.getFilePath());
                            rawFileInfo.setFileSize(data.getFileSize());

                            // 根据文件路径提取日期信息，构建ZIP中的相对路径
                            String zipRelativePath = buildZipRelativePath(data.getDeviceCode(), data.getFilePath(), data.getFileName());
                            rawFileInfo.setZipRelativePath(zipRelativePath);
                            info.setRawFileInfo(rawFileInfo);

                            log.info("成功准备原始文件信息 - 设备: {}, ZIP路径: {}",
                                     data.getDeviceCode(), zipRelativePath);
                        } catch (IOException e) {
                            log.error("获取文件大小失败 - 设备: {}, 文件路径: {}",
                                      data.getDeviceCode(), data.getFilePath(), e);
                        }
                    } else {
                        log.warn("原始文件不存在，跳过打包 - 设备: {}, 文件路径: {}",
                                 data.getDeviceCode(), data.getFilePath());
                    }
                } else {
                    log.warn("文件路径为空，跳过原始文件处理 - 设备: {}, 数据ID: {}",
                             data.getDeviceCode(), data.getId());
                }
            } else {
                log.info("未选择包含原始文件，跳过原始文件处理 - 设备: {}", data.getDeviceCode());
            }

            result.put(data.getId(), info);
        }

        // 统计原始文件信息
        long hasRawFileCount = result.values().stream()
                .filter(info -> info.getRawFileInfo() != null)
                .count();
        log.info("详细检测数据收集完成 - 总条数: {}, 包含原始文件信息: {}",
                 result.size(), hasRawFileCount);

        return result;
    }

    @Override
    public DeviceExportResult getExportProgress(String taskId) {
        return exportTaskCache.get(taskId);
    }

    @Override
    public String getDownloadPath(String taskId) {
        DeviceExportResult result = exportTaskCache.get(taskId);
        if (result != null && result.isSuccess() && StringUtils.hasText(result.getDownloadUrl())) {
            return result.getDownloadUrl();
        }
        return null;
    }

    @Override
    public boolean cancelExport(String taskId) {
        // 实现取消逻辑
        exportTaskCache.remove(taskId);

        DeviceExportLog exportLog = getByTaskId(taskId);
        if (exportLog != null) {
            exportLog.setExportStatus(2); // 失败状态
            exportLog.setErrorMessage("用户取消导出");
            exportLog.setCompleteTime(LocalDateTime.now());
            updateById(exportLog);
        }

        return true;
    }

    @Override
    public Page<DeviceExportLog> getExportHistory(int pageNum, int pageSize, String taskId, Integer exportStatus, String startTime, String endTime) {
        Page<DeviceExportLog> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<DeviceExportLog> wrapper = new LambdaQueryWrapper<>();

        // 任务ID模糊查询
        if (StringUtils.hasText(taskId)) {
            wrapper.like(DeviceExportLog::getTaskId, taskId);
        }

        // 导出状态筛选
        if (exportStatus != null) {
            wrapper.eq(DeviceExportLog::getExportStatus, exportStatus);
        }

        // 时间范围筛选
        if (StringUtils.hasText(startTime)) {
            try {
                LocalDateTime start = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                wrapper.ge(DeviceExportLog::getCreateTime, start);
            } catch (Exception e) {
                log.warn("解析开始时间失败: {}", startTime, e);
            }
        }

        if (StringUtils.hasText(endTime)) {
            try {
                LocalDateTime end = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                wrapper.le(DeviceExportLog::getCreateTime, end);
            } catch (Exception e) {
                log.warn("解析结束时间失败: {}", endTime, e);
            }
        }

        // 按创建时间倒序排列
        wrapper.orderByDesc(DeviceExportLog::getCreateTime);

        return page(page, wrapper);
    }

    @Override
    public int cleanupExpiredExports(int expireDays) {
        // 实现清理过期导出文件的逻辑
        LocalDateTime expireTime = LocalDateTime.now().minusDays(expireDays);

        LambdaQueryWrapper<DeviceExportLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(DeviceExportLog::getCreateTime, expireTime);
        wrapper.isNotNull(DeviceExportLog::getFilePath);

        List<DeviceExportLog> expiredLogs = list(wrapper);
        int cleanedCount = 0;

        for (DeviceExportLog deviceExportLog : expiredLogs) {
            try {
                Path filePath = Paths.get(deviceExportLog.getFilePath());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    cleanedCount++;
                }
            } catch (Exception e) {
                log.warn("清理过期导出文件失败: {}", deviceExportLog.getFilePath(), e);
            }
        }

        return cleanedCount;
    }

    // 辅助方法
    private DeviceExportLog getByTaskId(String taskId) {
        LambdaQueryWrapper<DeviceExportLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceExportLog::getTaskId, taskId);
        return getOne(wrapper);
    }

    private void updateProgress(String taskId, int progress, String step) {
        // 更新内存缓存
        DeviceExportResult result = exportTaskCache.get(taskId);
        if (result != null) {
            result.setProgress(progress);
            result.setCurrentStep(step);
        }

        // 更新数据库
        DeviceExportLog exportLog = getByTaskId(taskId);
        if (exportLog != null) {
            exportLog.setProgress(progress);
            exportLog.setCurrentStep(step);
            updateById(exportLog);
        }

        log.info("导出任务 {} 进度更新: {}% - {}", taskId, progress, step);
    }

    private ExportedDeviceData createExportData(List<Device> devices,
                                              List<DeviceTemplateBinding> bindings,
                                              List<ExcelTemplate> templates,
                                              Map<Long, ExportedDeviceData.DetectionDataInfo> detectionDataMap,
                                              DeviceExportRequest request) {
        ExportedDeviceData exportData = new ExportedDeviceData();

        // 设置元数据
        ExportedDeviceData.ExportMetadata metadata = new ExportedDeviceData.ExportMetadata();
        metadata.setExportTime(LocalDateTime.now());
        metadata.setTimeRangeStart(request.getActualStartTime());
        metadata.setTimeRangeEnd(request.getActualEndTime());
        metadata.setDeviceCodes(request.getDeviceCodes());

        ExportedDeviceData.ExportOptions options = new ExportedDeviceData.ExportOptions();
        options.setIncludeRawFiles(request.getIncludeRawFiles());
        options.setIncludeParsedData(request.getIncludeParsedData());
        options.setIncludeTemplateInfo(request.getIncludeTemplateInfo());
        metadata.setOptions(options);

        exportData.setMetadata(metadata);

        // 转换设备信息
        List<ExportedDeviceData.DeviceInfo> deviceInfoList = devices.stream()
            .map(this::convertToDeviceInfo)
            .collect(Collectors.toList());
        exportData.setDevices(deviceInfoList);

        // 转换模板绑定信息
        List<ExportedDeviceData.DeviceTemplateBindingInfo> bindingInfoList = bindings.stream()
            .map(this::convertToBindingInfo)
            .collect(Collectors.toList());
        exportData.setTemplateBindings(bindingInfoList);

        // 转换Excel模板信息
        List<ExportedDeviceData.ExcelTemplateInfo> templateInfoList = templates.stream()
            .map(this::convertToTemplateInfo)
            .collect(Collectors.toList());
        exportData.setExcelTemplates(templateInfoList);

        // 设置检测数据
        exportData.setDetectionData(new ArrayList<>(detectionDataMap.values()));

        return exportData;
    }

    private ExportedDeviceData.DeviceInfo convertToDeviceInfo(Device device) {
        ExportedDeviceData.DeviceInfo info = new ExportedDeviceData.DeviceInfo();
        BeanUtils.copyProperties(device, info);
        return info;
    }

    private ExportedDeviceData.DeviceTemplateBindingInfo convertToBindingInfo(DeviceTemplateBinding binding) {
        ExportedDeviceData.DeviceTemplateBindingInfo info = new ExportedDeviceData.DeviceTemplateBindingInfo();
        BeanUtils.copyProperties(binding, info);
        return info;
    }

    private ExportedDeviceData.ExcelTemplateInfo convertToTemplateInfo(ExcelTemplate template) {
        ExportedDeviceData.ExcelTemplateInfo info = new ExportedDeviceData.ExcelTemplateInfo();
        BeanUtils.copyProperties(template, info);
        return info;
    }

    private String generateZipFile(String taskId, ExportedDeviceData exportData, DeviceExportRequest request)
            throws Exception {
        // 创建导出目录
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String exportDir = uploadPath + "/exports";
        Path exportDirPath = Paths.get(exportDir);
        if (!Files.exists(exportDirPath)) {
            Files.createDirectories(exportDirPath);
        }

        String zipFileName = "device_export_" + timestamp + "_" + taskId.substring(taskId.length() - 8) + ".zip";
        String zipFilePath = exportDir + "/" + zipFileName;

        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFilePath))) {
            // 添加元数据文件
            addJsonToZip(zos, "metadata.json", exportData.getMetadata());

            // 添加设备信息文件
            addJsonToZip(zos, "devices/device_info.json", exportData.getDevices());

            // 添加模板绑定信息文件
            addJsonToZip(zos, "devices/device_templates.json", exportData.getTemplateBindings());

            // 添加Excel模板信息文件
            if (request.getIncludeTemplateInfo()) {
                addJsonToZip(zos, "templates/excel_templates.json", exportData.getExcelTemplates());
            }

            // 添加检测数据文件
            if (request.getIncludeParsedData()) {
                addJsonToZip(zos, "detection_data/parsed_data.json", exportData.getDetectionData());
            }

            // 添加原始文件
            if (request.getIncludeRawFiles()) {
                log.info("开始添加原始文件到ZIP - 任务ID: {}, 检测数据条数: {}",
                         taskId, exportData.getDetectionData().size());
                addRawFilesToZip(zos, exportData.getDetectionData());
                log.info("原始文件添加完成 - 任务ID: {}", taskId);
            } else {
                log.info("跳过原始文件添加 - 任务ID: {}, includeRawFiles: false", taskId);
            }
        }

        return zipFilePath;
    }

    private void addJsonToZip(ZipOutputStream zos, String fileName, Object data) throws Exception {
        ZipEntry entry = new ZipEntry(fileName);
        zos.putNextEntry(entry);

        String jsonContent = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(data);
        zos.write(jsonContent.getBytes("UTF-8"));
        zos.closeEntry();
    }

    /**
     * 构建ZIP文件中的相对路径
     * 格式: raw_files/设备编码/日期/文件名
     */
    private String buildZipRelativePath(String deviceCode, String filePath, String fileName) {
        try {
            Path path = Paths.get(filePath);
            Path parent = path.getParent();

            // 尝试从路径中提取日期信息
            String dateFolder = null;
            if (parent != null) {
                String parentName = parent.getFileName().toString();
                // 检查是否是日期格式 (yyyy-MM-dd)
                if (parentName.matches("\\d{4}-\\d{2}-\\d{2}")) {
                    dateFolder = parentName;
                }
            }

            if (dateFolder != null) {
                return "raw_files/" + deviceCode + "/" + dateFolder + "/" + fileName;
            } else {
                return "raw_files/" + deviceCode + "/" + fileName;
            }
        } catch (Exception e) {
            log.warn("构建ZIP相对路径失败，使用默认路径: {}", filePath, e);
            return "raw_files/" + deviceCode + "/" + fileName;
        }
    }

    private void addRawFilesToZip(ZipOutputStream zos, List<ExportedDeviceData.DetectionDataInfo> detectionDataList) {
        log.info("开始添加原始文件到ZIP，总检测数据条数: {}", detectionDataList.size());

        int totalRawFiles = 0;
        int successCount = 0;
        int skipCount = 0;
        int errorCount = 0;

        for (ExportedDeviceData.DetectionDataInfo dataInfo : detectionDataList) {
            String deviceCode = dataInfo.getDeviceCode();

            if (dataInfo.getRawFileInfo() != null) {
                totalRawFiles++;
                try {
                    ExportedDeviceData.RawFileInfo rawFileInfo = dataInfo.getRawFileInfo();
                    Path sourceFile = Paths.get(rawFileInfo.getFilePath());

                    log.info("处理原始文件 - 设备: {}, 文件: {}, ZIP路径: {}",
                             deviceCode, rawFileInfo.getFilePath(), rawFileInfo.getZipRelativePath());

                    if (Files.exists(sourceFile)) {
                        try {
                            long fileSize = Files.size(sourceFile);
                            log.info("开始添加文件到ZIP - 设备: {}, 文件大小: {} bytes", deviceCode, fileSize);

                            ZipEntry entry = new ZipEntry(rawFileInfo.getZipRelativePath());
                            entry.setSize(fileSize);
                            zos.putNextEntry(entry);

                            long copiedBytes = Files.copy(sourceFile, zos);
                            zos.closeEntry();

                            successCount++;
                            log.info("成功添加原始文件到ZIP - 设备: {}, ZIP路径: {}, 复制字节数: {}",
                                     deviceCode, rawFileInfo.getZipRelativePath(), copiedBytes);
                        } catch (IOException e) {
                            errorCount++;
                            log.error("复制文件到ZIP失败 - 设备: {}, 文件: {}",
                                      deviceCode, rawFileInfo.getFilePath(), e);
                        }
                    } else {
                        skipCount++;
                        log.warn("原始文件不存在，跳过添加 - 设备: {}, 文件路径: {}",
                                 deviceCode, rawFileInfo.getFilePath());
                    }
                } catch (Exception e) {
                    errorCount++;
                    log.error("处理原始文件失败 - 设备: {}, 文件: {}",
                              deviceCode, dataInfo.getRawFileInfo().getFilePath(), e);
                }
            } else {
                log.debug("检测数据无原始文件信息 - 设备: {}", deviceCode);
            }
        }

        log.info("原始文件添加完成 - 总数据条数: {}, 有原始文件: {}, 成功添加: {}, 跳过: {}, 错误: {}",
                 detectionDataList.size(), totalRawFiles, successCount, skipCount, errorCount);
    }

    private void completeExport(String taskId, String zipFilePath, DeviceExportResult result, DeviceExportLog exportLog) {
        try {
            Path zipPath = Paths.get(zipFilePath);
            long fileSize = Files.size(zipPath);
            String fileName = zipPath.getFileName().toString();
            String downloadUrl = domain + filePrefix + "/exports/" + fileName;

            // 更新结果
            result.setStatus(1); // 成功
            result.setProgress(100); // 确保进度为100%
            result.setCurrentStep("导出完成");
            result.setCompleteTime(LocalDateTime.now());
            result.setFilePath(zipFilePath);
            result.setFileName(fileName);
            result.setFileSize(fileSize);
            result.setDownloadUrl(downloadUrl);

            // 更新缓存
            exportTaskCache.put(taskId, result);

            // 更新数据库
            exportLog.setExportStatus(1);
            exportLog.setProgress(100);
            exportLog.setCurrentStep("导出完成");
            exportLog.setFilePath(zipFilePath);
            exportLog.setFileName(fileName);
            exportLog.setFileSize(fileSize);
            exportLog.setCompleteTime(LocalDateTime.now());
            updateById(exportLog);

            log.info("导出任务完成: {}, 文件大小: {} bytes", taskId, fileSize);

        } catch (Exception e) {
            log.error("完成导出任务时发生错误: {}", taskId, e);
            failExport(taskId, "完成导出时发生错误: " + e.getMessage(), result, exportLog);
        }
    }

    private void failExport(String taskId, String errorMessage, DeviceExportResult result, DeviceExportLog exportLog) {
        result.setStatus(2); // 失败
        result.setCurrentStep("导出失败");
        result.setCompleteTime(LocalDateTime.now());
        result.setErrorMessage(errorMessage);

        // 更新缓存
        exportTaskCache.put(taskId, result);

        exportLog.setExportStatus(2);
        exportLog.setCurrentStep("导出失败");
        exportLog.setErrorMessage(errorMessage);
        exportLog.setCompleteTime(LocalDateTime.now());
        updateById(exportLog);

        log.error("导出任务失败: {}, 错误信息: {}", taskId, errorMessage);
    }
}
