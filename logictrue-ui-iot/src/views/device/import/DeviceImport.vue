<template>
  <div class="device-import-container">
    <!-- 文件上传卡片 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="card-title">设备数据导入</span>
      </div>

      <div class="upload-section">
        <el-upload
          ref="upload"
          class="upload-demo"
          drag
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :file-list="fileList"
          :limit="1"
          :auto-upload="false"
          accept=".zip"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将ZIP文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            只能上传ZIP格式的导出文件，且不超过100MB
          </div>
        </el-upload>

        <div class="upload-actions">
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            <i class="el-icon-upload2"></i>
            上传文件
          </el-button>
          <el-button @click="clearFiles">清空</el-button>
        </div>
      </div>
    </el-card>

    <!-- 验证结果卡片 -->
    <el-card v-if="validationResult" class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span class="card-title">数据验证结果</span>
        <el-tag :type="getValidationStatusType(validationResult.status)" style="float: right;">
          {{ getValidationStatusText(validationResult.status) }}
        </el-tag>
      </div>

      <div v-if="validationResult.status === 1" class="validation-success">
        <!-- 导入统计 -->
        <div v-if="validationResult.statistics" class="import-statistics">
          <h4>导入预览</h4>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ validationResult.statistics.totalDeviceCount || 0 }}</div>
                <div class="stat-label">设备数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ validationResult.statistics.templateBindingImportCount || 0 }}</div>
                <div class="stat-label">模板绑定</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ formatNumber(validationResult.statistics.basicFieldImportCount) }}</div>
                <div class="stat-label">基础字段</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ formatNumber(validationResult.statistics.tableDataRowImportCount) }}</div>
                <div class="stat-label">表格数据行</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 导入配置 -->
        <div class="import-config">
          <h4>导入配置</h4>
          <el-form ref="importForm" :model="importForm" :rules="importRules" label-width="150px">
            <el-form-item label="导入模式" prop="importMode">
              <el-radio-group v-model="importForm.importMode">
                <el-radio label="REPLACE">完全替换</el-radio>
                <el-radio label="MERGE">合并导入</el-radio>
              </el-radio-group>
              <div class="form-tip">
                完全替换：删除现有数据后导入；合并导入：保留现有数据，仅导入新数据
              </div>
            </el-form-item>

            <el-form-item label="冲突处理策略" prop="conflictStrategy">
              <el-radio-group v-model="importForm.conflictStrategy">
                <el-radio label="SKIP">跳过冲突项</el-radio>
                <el-radio label="OVERRIDE">覆盖冲突项</el-radio>
                <el-radio label="ERROR">遇到冲突报错</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="覆盖选项">
              <el-checkbox-group v-model="overrideOptions">
                <el-checkbox label="overrideDeviceConfig">设备配置</el-checkbox>
                <el-checkbox label="overrideTemplateBinding">模板绑定</el-checkbox>
                <el-checkbox label="overrideDetectionData">检测数据</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="备注">
              <el-input
                v-model="importForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入导入备注"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="startImport" :loading="importing">
                <i class="el-icon-upload"></i>
                开始导入
              </el-button>
              <el-button @click="resetImportForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div v-else-if="validationResult.status === 2" class="validation-error">
        <el-alert
          title="验证失败"
          :description="validationResult.errorMessage"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <div v-else class="validation-progress">
        <el-progress
          :percentage="validationResult.progress || 0"
          :status="validationResult.status === 2 ? 'exception' : null"
        />
        <div class="progress-text">
          {{ validationResult.currentStep || '验证中...' }}
        </div>
      </div>
    </el-card>

    <!-- 导入进度对话框 -->
    <import-progress-dialog
      :visible.sync="progressVisible"
      :task-id="currentTaskId"
      @close="handleProgressClose"
    />


  </div>
</template>

<script>
import ImportProgressDialog from './components/ImportProgressDialog'
import {
  uploadImportFile,
  validateImportData,
  executeImport,
  cancelDeviceImport
} from '@/api/device/import'

export default {
  name: 'DeviceImport',
  components: {
    ImportProgressDialog
  },
  data() {
    return {
      // 上传相关
      fileList: [],
      uploading: false,

      // 验证结果
      validationResult: null,
      validationPolling: null,

      // 导入表单
      importForm: {
        taskId: null,
        importMode: 'REPLACE',
        conflictStrategy: 'OVERRIDE',
        remark: ''
      },
      overrideOptions: ['overrideDeviceConfig', 'overrideTemplateBinding', 'overrideDetectionData'],
      importRules: {
        importMode: [
          { required: true, message: '请选择导入模式', trigger: 'change' }
        ],
        conflictStrategy: [
          { required: true, message: '请选择冲突处理策略', trigger: 'change' }
        ]
      },

      // 导入状态
      importing: false,
      progressVisible: false,
      currentTaskId: null
    }
  },
  mounted() {
    // 页面初始化
  },
  beforeDestroy() {
    this.stopValidationPolling()
  },
  methods: {
    // 文件上传前检查
    beforeUpload(file) {
      const isZip = file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')
      const isLt100M = file.size / 1024 / 1024 < 100

      if (!isZip) {
        this.$message.error('只能上传ZIP格式的文件!')
        return false
      }
      if (!isLt100M) {
        this.$message.error('上传文件大小不能超过 100MB!')
        return false
      }
      return true
    },

    // 文件选择变化处理
    handleFileChange(file, fileList) {
      this.fileList = fileList
    },

    // 提交上传
    async submitUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      const file = this.fileList[0].raw || this.fileList[0]
      const formData = new FormData()
      formData.append('file', file)

      this.uploading = true
      try {
        const response = await uploadImportFile(formData)
        await this.handleUploadSuccess(response, file, this.fileList)
      } catch (error) {
        this.handleUploadError(error, file, this.fileList)
      }
    },

    // 上传成功
    async handleUploadSuccess(response, file, fileList) {
      this.uploading = false
      if (response.code === 200) {
        this.$message.success('文件上传成功，正在验证数据...')
        this.importForm.taskId = response.data
        this.startValidationPolling()
      } else {
        this.$message.error(response.msg || '文件上传失败')
      }
    },

    // 上传失败
    handleUploadError(err, file, fileList) {
      this.uploading = false
      console.error('上传失败:', err)
      this.$message.error('文件上传失败')
    },

    // 清空文件
    clearFiles() {
      this.fileList = []
      this.validationResult = null
      this.importForm.taskId = null
      this.stopValidationPolling()
    }
  }
}
</script>

<style scoped>
.device-import-container {
  padding: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.upload-section {
  text-align: center;
}

.upload-actions {
  margin-top: 20px;
}

.validation-success {
  padding: 10px 0;
}

.import-statistics {
  margin-bottom: 30px;
}

.import-statistics h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.import-config h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.validation-progress {
  padding: 20px 0;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #606266;
}
</style>
